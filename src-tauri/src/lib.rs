mod database;
mod device_commands; // 用于构建设备指令的模块
mod filters;
mod hid;

use chrono::{DateTime, Utc};
use database::DbConnection;
use device_commands::{ReadingMode, SleepMode}; // 导入新的枚举
use hid::{Hid<PERSON><PERSON><PERSON><PERSON><PERSON>, HidApiState};
use std::fs;
use std::path::PathBuf;
use std::sync::{Arc, Mutex};
use tauri::{plugin::TauriPlugin, AppH<PERSON>le, Manager, State};
use tauri_plugin_log::{Target, TargetKind};

// --- 构建信息 ---

#[derive(serde::Serialize, Clone)]
pub struct BuildInfo {
    version: String,
    build_timestamp: String,
}

#[tauri::command]
fn get_build_info() -> BuildInfo {
    BuildInfo {
        version: env!("CARGO_PKG_VERSION").to_string(),
        build_timestamp: env!("BUILD_TIMESTAMP").to_string(),
    }
}

// --- 数据库指令 ---

#[tauri::command]
fn get_all_readings(
    db: State<DbConnection>,
) -> Result<Vec<database::FrontendSensorReading>, String> {
    database::get_all_readings(&db.0).map_err(|e| e.to_string())
}

// --- HID 控制指令 ---

#[tauri::command]
fn get_device_list(hid_state: State<HidApiState>) -> Result<Vec<hid::DeviceInfo>, String> {
    log::info!("获取设备列表中...");
    hid::list_hid_devices(&hid_state)
}

#[tauri::command]
fn start_reading<R: tauri::Runtime>(
    app: AppHandle<R>,
    device_id: String,
    actor_handle: State<HidActorHandle>,
    hid_state: State<HidApiState>,
    db: State<DbConnection>,
) -> Result<(), String> {
    hid::start_hid_actor(
        app,
        device_id,
        &actor_handle,
        &hid_state,
        db.inner().clone(),
    )
}

#[tauri::command]
fn stop_reading(actor_handle: State<HidActorHandle>) -> Result<(), String> {
    log::info!("停止 HID Actor...");

    if let Err(e) = hid::stop_reading(&actor_handle) {
        log::error!("停止 HID Actor 失败: {}", e);
        return Err(e);
    }
    log::info!("HID Actor 停止成功。");
    Ok(())
}

#[tauri::command]
fn set_reading_mode(actor_handle: State<HidActorHandle>, mode: String) -> Result<(), String> {
    log::info!("设置读取模式为: {}", mode);
    let reading_mode = match mode.as_str() {
        "parsed" => ReadingMode::Parsed,
        "raw" => ReadingMode::Raw,
        _ => return Err("无效的读取模式".to_string()),
    };
    let data = device_commands::build_set_reading_mode_cmd(reading_mode);

    if let Err(e) = hid::write_to_device(&actor_handle, data) {
        log::error!("设置读取模式失败: {}", e);
        return Err(e);
    }
    log::info!("读取模式设置成功。");
    Ok(())
}

#[tauri::command]
fn set_timestamp(actor_handle: State<HidActorHandle>) -> Result<(), String> {
    log::info!("发送 'set timestamp' 指令...");
    let data = device_commands::build_set_timestamp_cmd();

    if let Err(e) = hid::write_to_device(&actor_handle, data) {
        log::error!("发送 'set timestamp' 指令失败: {}", e);
        return Err(e);
    }
    log::info!("'set timestamp' 指令发送成功。");
    Ok(())
}

#[tauri::command]
fn set_broadcast_name(name: String, actor_handle: State<HidActorHandle>) -> Result<(), String> {
    log::info!("设置广播名称为: {}", name);
    let data = device_commands::build_set_broadcast_name_cmd(&name);

    if let Err(e) = hid::write_to_device(&actor_handle, data) {
        log::error!("设置广播名称失败: {}", e);
        return Err(e);
    }
    log::info!("广播名称设置成功。");
    Ok(())
}

#[tauri::command]
fn set_sleep_mode(mode: u8, actor_handle: State<HidActorHandle>) -> Result<(), String> {
    log::info!("设置休眠模式为: {}", mode);
    let sleep_mode = match mode {
        1 => SleepMode::Normal,
        2 => SleepMode::Sleep,
        _ => return Err("无效的休眠模式".to_string()),
    };
    let data = device_commands::build_set_sleep_mode_cmd(sleep_mode);
    if let Err(e) = hid::write_to_device(&actor_handle, data) {
        log::error!("设置休眠模式失败: {}", e);
        return Err(e);
    }
    log::info!("休眠模式设置成功。");
    Ok(())
}

#[tauri::command]
fn unbind_device(actor_handle: State<HidActorHandle>) -> Result<(), String> {
    log::info!("删除设备绑定信息...");

    let data = device_commands::build_unbind_device_cmd();
    if let Err(e) = hid::write_to_device(&actor_handle, data) {
        log::error!("设备绑定信息删除失败: {}", e);
        return Err(e);
    }
    log::info!("设备绑定信息删除成功。");
    Ok(())
}

// --- 数据导出指令 ---

/// 将读数序列化为 CSV 字符串的辅助函数。
fn serialize_readings_to_csv(
    readings: Vec<database::FrontendSensorReading>,
) -> Result<String, String> {
    let mut wtr = csv::Writer::from_writer(vec![]);
    for reading in readings {
        wtr.serialize(reading).map_err(|e| {
            let err_msg = format!("序列化读数至 CSV 失败: {}", e);
            log::error!("{}", err_msg);
            err_msg
        })?;
    }
    wtr.flush().map_err(|e| e.to_string())?;
    String::from_utf8(wtr.into_inner().map_err(|e| e.to_string())?).map_err(|e| e.to_string())
}

/// 将 CSV 数据写入用户下载目录的辅助函数。
fn write_csv_to_download_dir(csv_data: &str) -> Result<PathBuf, String> {
    let download_dir = dirs::download_dir().ok_or_else(|| "获取下载目录失败".to_string())?;
    let file_name = format!("export_{}.csv", Utc::now().format("%Y%m%d_%H%M%S"));
    let file_path = download_dir.join(file_name);

    fs::write(&file_path, csv_data).map_err(|e| {
        let err_msg = format!(
            "写入 CSV 文件至 {} 失败: {}",
            file_path.to_string_lossy(),
            e
        );
        log::error!("{}", err_msg);
        err_msg
    })?;

    Ok(file_path)
}

#[tauri::command]
fn export_csv(
    start_time: String,
    end_time: String,
    db: State<DbConnection>,
) -> Result<String, String> {
    let start = DateTime::parse_from_rfc3339(&start_time)
        .map_err(|e| e.to_string())?
        .with_timezone(&chrono::Utc);
    let end = DateTime::parse_from_rfc3339(&end_time)
        .map_err(|e| e.to_string())?
        .with_timezone(&chrono::Utc);

    let readings =
        database::get_readings_by_time_range(&db.0, start, end).map_err(|e| e.to_string())?;

    let csv_data = serialize_readings_to_csv(readings)?;
    let file_path = write_csv_to_download_dir(&csv_data)?;

    Ok(file_path.to_string_lossy().to_string())
}

// --- 应用设置 ---

fn setup_logging() -> TauriPlugin<tauri::Wry> {
    tauri_plugin_log::Builder::default()
        .format(|out, message, record| {
            out.finish(format_args!(
                "[{} {}] {}",
                record.level(),
                record.target(),
                message
            ))
        })
        .timezone_strategy(tauri_plugin_log::TimezoneStrategy::UseLocal)
        .targets([
            Target::new(TargetKind::Stdout),
            Target::new(TargetKind::Webview),
            Target::new(TargetKind::LogDir {
                file_name: Some("MR01".into()),
            }),
        ])
        .level(log::LevelFilter::Debug)
        .build()
}

fn setup_database(app: &AppHandle) -> Result<DbConnection, anyhow::Error> {
    let conn = database::init(app)?;
    let db_connection = DbConnection(Arc::new(Mutex::new(conn)));
    app.manage(db_connection.clone());
    Ok(db_connection)
}

fn setup_hid(app: &AppHandle) -> Result<(), anyhow::Error> {
    let api = hidapi::HidApi::new().map_err(|e| anyhow::anyhow!("初始化 HID API 失败: {}", e))?;
    app.manage(HidApiState(Arc::new(Mutex::new(api))));
    app.manage(HidActorHandle::new());
    Ok(())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_fs::init())
        .plugin(setup_logging())
        .plugin(tauri_plugin_opener::init())
        .setup(|app| {
            setup_database(app.handle())?;
            setup_hid(app.handle())?;
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            // 数据库
            get_all_readings,
            export_csv,
            // HID 控制
            get_device_list,
            start_reading,
            stop_reading,
            // 设备指令
            set_reading_mode,
            set_timestamp,
            set_broadcast_name,
            set_sleep_mode,
            unbind_device,
            // 元信息
            get_build_info
        ])
        .run(tauri::generate_context!())
        .expect("运行 Tauri 应用时出错");
}
