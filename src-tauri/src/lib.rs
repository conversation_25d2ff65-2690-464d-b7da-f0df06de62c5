mod database;
mod filters;
mod hid;

use chrono::{DateTime, Utc};
use database::DbConnection;
use hid::{Hid<PERSON><PERSON><PERSON><PERSON><PERSON>, HidApiState};
use std::sync::{Arc, Mutex};
use tauri::{plugin::TauriPlugin, <PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, State};
use tauri_plugin_log::{Target, TargetKind};

// New struct to hold build information
#[derive(serde::Serialize, Clone)]
pub struct BuildInfo {
    version: String,
    build_timestamp: String, // UNIX timestamp as a string
}

// New Tauri command to get build information
#[tauri::command]
fn get_build_info() -> BuildInfo {
    BuildInfo {
        version: env!("CARGO_PKG_VERSION").to_string(),
        build_timestamp: env!("BUILD_TIMESTAMP").to_string(),
    }
}

// --- Tauri Commands ---

#[tauri::command]
fn get_all_readings(
    db: State<DbConnection>,
) -> Result<Vec<database::FrontendSensorReading>, String> {
    database::get_all_readings(&db.0).map_err(|e| e.to_string())
}

#[tauri::command]
fn get_device_list(hid_state: State<HidApiState>) -> Result<Vec<hid::DeviceInfo>, String> {
    log::info!("Getting device list...");
    hid::list_hid_devices(&hid_state)
}

#[tauri::command]
fn start_reading<R: tauri::Runtime>(
    app: AppHandle<R>,
    device_id: String,
    actor_handle: State<HidActorHandle>,
    hid_state: State<HidApiState>,
    db: State<DbConnection>,
) -> Result<(), String> {
    hid::start_hid_actor(
        app,
        device_id,
        &actor_handle,
        &hid_state,
        db.inner().clone(),
    )
}

#[tauri::command]
fn stop_reading(actor_handle: State<HidActorHandle>) -> Result<(), String> {
    log::info!("Attempting to stop HID actor...");
    hid::stop_hid_actor(&actor_handle)
}

#[tauri::command]
fn set_reading_mode(actor_handle: State<HidActorHandle>, mode: String) -> Result<(), String> {
    log::info!("Reading mode set to: {}", mode);

    let mut data = vec![0u8; 9];
    data[0] = 0x01; // Report ID
    data[1] = 0x04; // Cmd Type: Set Timestamp

    if mode == "parsed" {
        data[2] = 0x01;
    } else if mode == "raw" {
        data[2] = 0x00;
    } else {
        return Err("Invalid reading mode".to_string());
    }
    hid::write_to_device(&actor_handle, data)
}

#[tauri::command]
fn set_timestamp(actor_handle: State<HidActorHandle>) -> Result<(), String> {
    // Example command: Report ID 1, command type 2 (Set Time), value can be 0.
    log::info!("Attempting to set broadcast timestamp...");

    let mut data = vec![0u8; 9];
    data[0] = 0x01; // Report ID
    data[1] = 0x03; // Cmd Type: Set Timestamp

    hid::write_to_device(&actor_handle, data)
}

#[tauri::command]
fn set_broadcast_name(name: String, actor_handle: State<HidActorHandle>) -> Result<(), String> {
    log::info!("Attempting to set broadcast name to: {}", name);

    let mut data = vec![0u8; 9];
    data[0] = 0x01; // Report ID
    data[1] = 0x05; // Cmd Type: Set Name
    let name_bytes = name.as_bytes();
    let len = std::cmp::min(name_bytes.len(), 6);
    data[2] = len as u8; // First byte for length
    data[3..3 + len].copy_from_slice(&name_bytes[..len]);
    hid::write_to_device(&actor_handle, data)
}

#[tauri::command]
fn unbind_device(mode: u8, actor_handle: State<HidActorHandle>) -> Result<(), String> {
    log::info!("Attempting to ubind device...");

    let mut data = vec![0u8; 9];
    data[0] = 0x01; // Report ID
    data[1] = 0x06; // Cmd Type: Sleep mode

    if mode == 1 {
        data[2] = 0x00;
    } else if mode == 2 {
        data[2] = 0x01;
    } else {
        return Err("Invalid sleep mode".to_string());
    }

    let _ = hid::write_to_device(&actor_handle, data);

    Ok(())
}

#[tauri::command]
fn set_sleep_mode(mode: u8, actor_handle: State<HidActorHandle>) -> Result<(), String> {
    log::info!("Attempting to set sleep mode to: {}", mode);

    let mut data = vec![0u8; 9];
    data[0] = 0x01; // Report ID
    data[1] = 0x06; // Cmd Type: Set Sleep Mode
    if mode == 1 {
        data[2] = 0x00;
    } else if mode == 2 {
        data[2] = 0x01;
    } else {
        return Err("Invalid sleep mode".to_string());
    }

    if let Err(e) = hid::write_to_device(&actor_handle, data) {
        log::error!("Failed to set sleep mode: {}", e);
        return Err(e);
    }

    Ok(())
}

#[tauri::command]
fn export_csv(
    start_time: String,
    end_time: String,
    db: State<DbConnection>,
) -> Result<String, String> {
    let start = DateTime::parse_from_rfc3339(&start_time)
        .map_err(|e| e.to_string())?
        .with_timezone(&chrono::Utc);
    let end = DateTime::parse_from_rfc3339(&end_time)
        .map_err(|e| e.to_string())?
        .with_timezone(&chrono::Utc);

    let readings =
        database::get_readings_by_time_range(&db.0, start, end).map_err(|e| e.to_string())?;

    let mut wtr = csv::Writer::from_writer(vec![]);
    for reading in readings {
        if let Err(e) = wtr.serialize(reading).map_err(|e| e.to_string()) {
            log::error!("Failed to serialize reading to CSV: {}", e);
        }
    }
    wtr.flush().map_err(|e| e.to_string())?;
    let csv_data = String::from_utf8(wtr.into_inner().map_err(|e| e.to_string())?)
        .map_err(|e| e.to_string())?;

    let download_dir =
        dirs::download_dir().ok_or("Failed to get download directory".to_string())?;
    let file_name = format!(
        "export_{}.csv",
        Utc::now().format("%Y%m%d_%H%M%S").to_string()
    );
    let file_path = download_dir.join(file_name);

    if let Err(e) = std::fs::write(&file_path, csv_data) {
        log::error!(
            "Failed to write CSV file to {}: {}",
            file_path.to_string_lossy(),
            e
        );
        return Err(e.to_string());
    }

    Ok(file_path.to_string_lossy().to_string())
}

fn setup_logging() -> TauriPlugin<tauri::Wry> {
    tauri_plugin_log::Builder::default()
        .format(|out, message, record| {
            out.finish(format_args!(
                "[{} {}] {}",
                record.level(),
                record.target(),
                message
            ))
        })
        .timezone_strategy(tauri_plugin_log::TimezoneStrategy::UseLocal)
        .targets([
            Target::new(TargetKind::Stdout),
            Target::new(TargetKind::Webview),
            Target::new(TargetKind::LogDir {
                file_name: Some("MR01".into()),
            }),
        ])
        .level(log::LevelFilter::Debug)
        .build()
}

fn setup_database(app: &AppHandle) -> Result<DbConnection, anyhow::Error> {
    let conn = database::init(app)?;
    let db_connection = DbConnection(Arc::new(Mutex::new(conn)));
    app.manage(db_connection.clone());
    Ok(db_connection)
}

fn setup_hid(app: &AppHandle) -> Result<(), anyhow::Error> {
    let api =
        hidapi::HidApi::new().map_err(|e| anyhow::anyhow!("Failed to init HID API: {}", e))?;
    app.manage(HidApiState(Arc::new(Mutex::new(api))));
    app.manage(HidActorHandle::new());
    Ok(())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_fs::init())
        .plugin(setup_logging())
        .plugin(tauri_plugin_opener::init())
        .setup(|app| {
            setup_database(app.handle())?;
            setup_hid(app.handle())?;
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            get_all_readings,
            get_device_list,
            start_reading,
            stop_reading,
            set_reading_mode,
            set_timestamp,
            set_broadcast_name,
            unbind_device,
            export_csv,
            set_sleep_mode,
            get_build_info // Add the new command here
        ])
        .run(tauri::generate_context!())
        .expect("Error while running Tauri application");
}
