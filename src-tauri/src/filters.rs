use ahrs::{<PERSON><PERSON>, <PERSON><PERSON><PERSON>};
use nalgebra::{SMatrix, SVector, UnitQuaternion, Vector3};
use vqf_rs::{Params, Quaternion as VQFQuaternion, VQF}; // 90°

// --- Attitude: 四元数输出 ---
#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, serde::Serialize)]
pub struct Attitude {
    pub x: f64,
    pub y: f64,
    pub z: f64,
    pub w: f64,
}

impl Default for Attitude {
    fn default() -> Self {
        Self {
            x: 0.0,
            y: 0.0,
            z: 0.0,
            w: 1.0,
        }
    }
}

// --- Madgwick Filter ---
pub struct MadgwickFilter {
    filter: Madgwick<f64>,
}

impl MadgwickFilter {
    pub fn new() -> Self {
        Self {
            filter: Madgwick::new(0.01_f64, 0.1_f64),
        }
    }

    pub fn update(
        &mut self,
        gyro: &Vector3<f64>,
        acc: &Vector3<f64>,
        mag: &Vector3<f64>,
    ) -> Attitude {
        let q = match self.filter.update(gyro, acc, mag) {
            Ok(quat) => quat.clone(),
            Err(_) => self.filter.quat.clone(),
        };

        Attitude {
            x: q.i,
            y: q.j,
            z: q.k,
            w: q.w,
        }
    }
}

// --- Kalman Filter for one axis ---
pub struct KalmanFilter1D {
    x: SVector<f64, 2>,
    p: SMatrix<f64, 2, 2>,
    q: SMatrix<f64, 2, 2>,
    r: f64,
}

impl KalmanFilter1D {
    pub fn new(q_angle: f64, q_bias: f64, r_measure: f64) -> Self {
        Self {
            x: SVector::zeros(),
            p: SMatrix::identity(),
            q: SMatrix::from_diagonal(&SVector::<f64, 2>::new(q_angle, q_bias)),
            r: r_measure,
        }
    }

    pub fn update(&mut self, rate: f64, angle_measure: f64, dt: f64) -> f64 {
        // --- Predict ---
        self.x[0] += dt * (rate - self.x[1]);
        let f = SMatrix::<f64, 2, 2>::new(1.0, -dt, 0.0, 1.0);
        self.p = &f * &self.p * f.transpose() + self.q;

        // --- Update ---
        let h = SMatrix::<f64, 1, 2>::new(1.0, 0.0);
        let y = angle_measure - (&h * &self.x)[(0, 0)];
        let s = (&h * &self.p * h.transpose())[(0, 0)] + self.r;
        let k = &self.p * h.transpose() / s;
        self.x += k * y;
        self.p = (SMatrix::identity() - k * h) * self.p;

        self.x[0]
    }
}

// --- Combined Kalman Filter ---
pub struct CombinedKalmanFilter {
    kf_roll: KalmanFilter1D,
    kf_pitch: KalmanFilter1D,
    yaw: f64,
    q: UnitQuaternion<f64>,
}

impl CombinedKalmanFilter {
    pub fn new() -> Self {
        Self {
            kf_roll: KalmanFilter1D::new(0.001, 0.003, 0.03),
            kf_pitch: KalmanFilter1D::new(0.001, 0.003, 0.03),
            yaw: 0.0,
            q: UnitQuaternion::identity(),
        }
    }

    pub fn update(&mut self, gyro: &Vector3<f64>, acc: &Vector3<f64>, dt: f64) -> Attitude {
        // 加速度计算 roll/pitch 测量角度
        let roll_acc = acc.y.atan2(acc.z);
        let pitch_acc = (-acc.x).atan2((acc.y * acc.y + acc.z * acc.z).sqrt());

        // 通过卡尔曼滤波估计 roll / pitch
        let roll = self.kf_roll.update(gyro.x, roll_acc, dt);
        let pitch = self.kf_pitch.update(gyro.y, pitch_acc, dt);
        self.yaw += gyro.z * dt;

        // 转为四元数
        self.q = UnitQuaternion::from_euler_angles(roll, pitch, self.yaw);

        let q_corrected = self.q;

        Attitude {
            x: q_corrected.i,
            y: q_corrected.j,
            z: q_corrected.k,
            w: q_corrected.w,
        }
    }
}

pub struct CombinedVQFFilter {
    vqf: VQF,
}

impl CombinedVQFFilter {
    pub fn new(dt: f64) -> Self {
        let params = Params::default();
        let tau_acc: Option<f64> = None;
        let tau_mag: Option<f64> = None;
        let params_opt: Option<Params> = Some(params);

        Self {
            vqf: VQF::new(dt, tau_acc, tau_mag, params_opt),
        }
    }

    pub fn update(
        &mut self,
        gyro: &Vector3<f64>,
        acc: &Vector3<f64>,
        mag: &Vector3<f64>,
    ) -> Attitude {
        let gyro_data: [f64; 3] = [gyro.x, gyro.y, gyro.z];
        let accel_data: [f64; 3] = [acc.x, acc.y, acc.z];
        let mag_data: Option<[f64; 3]> = Some([mag.x, mag.y, mag.z]);

        self.vqf.update(gyro_data, accel_data, mag_data);
        let orientation: VQFQuaternion = self.vqf.state().gyr_quat;

        Attitude {
            x: orientation.1 as f64,
            y: orientation.2 as f64,
            z: orientation.3 as f64,
            w: orientation.0 as f64,
        }
    }
}
