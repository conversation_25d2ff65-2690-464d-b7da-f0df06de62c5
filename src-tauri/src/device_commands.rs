//! 设备指令

/// Defines the sensor data mode.
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum ReadingMode {
    /// 原始值
    Raw,
    /// 解析值
    Parsed,
}

/// 休眠模式
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq)]
pub enum SleepMode {
    /// 普通休眠
    Normal,
    /// 船运休眠
    Sleep,
}



const REPORT_ID: u8 = 0x01;
const CMD_LEN: usize = 9;

// 命令类型
const CMD_TYPE_SET_BINDING_DEL: u8 = 0x02; // 删除绑定信息
const CMD_TYPE_SET_TIMESTAMP: u8 = 0x03; // 设置时间戳
const CMD_TYPE_SET_READING_MODE: u8 = 0x04; // 设置数据读取模式
const CMD_TYPE_SET_NAME: u8 = 0x05; // 设置广播名称
const CMD_TYPE_SET_SLEEP_MODE: u8 = 0x06; // 设置休眠/船运模式



/// 开始传输指令
pub fn build_start_streaming_cmd() -> Vec<u8> {
    vec![REPORT_ID, 0x01, 0x01]
}

/// 停止传输指令
pub fn build_stop_streaming_cmd() -> Vec<u8> {
    vec![REPORT_ID, 0x01, 0x00]
}

/// 设备读取模式
pub fn build_set_reading_mode_cmd(mode: ReadingMode) -> Vec<u8> {
    let mut data = vec![0u8; CMD_LEN];
    data[0] = REPORT_ID;
    data[1] = CMD_TYPE_SET_READING_MODE;
    data[2] = match mode {
        ReadingMode::Parsed => 0x01,
        ReadingMode::Raw => 0x00,
    };
    data
}

/// 设备时间戳设置（没有使用）
pub fn build_set_timestamp_cmd() -> Vec<u8> {
    let mut data = vec![0u8; CMD_LEN];
    data[0] = REPORT_ID;
    data[1] = CMD_TYPE_SET_TIMESTAMP;
    data
}

/// 设置广播名称
pub fn build_set_broadcast_name_cmd(name: &str) -> Vec<u8> {
    let mut data = vec![0u8; CMD_LEN];
    data[0] = REPORT_ID;
    data[1] = CMD_TYPE_SET_NAME;

    let name_bytes = name.as_bytes();
    let len = std::cmp::min(name_bytes.len(), 6);
    data[2] = len as u8;
    data[3..3 + len].copy_from_slice(&name_bytes[..len]);
    data
}

/// 设置休眠/船运模式
pub fn build_set_sleep_mode_cmd(mode: SleepMode) -> Vec<u8> {
    let mut data = vec![0u8; CMD_LEN];
    data[0] = REPORT_ID;
    data[1] = CMD_TYPE_SET_SLEEP_MODE;
    data[2] = match mode {
        SleepMode::Normal => 0x00,
        SleepMode::Sleep => 0x01,
    };
    data
}

pub fn build_unbind_device_cmd() -> Vec<u8> {
    let mut data = vec![0u8; CMD_LEN];
    data[0] = REPORT_ID;
    data[1] = CMD_TYPE_SET_BINDING_DEL;
    data
}
