// src-tauri/src/database.rs

use anyhow::Context;
use chrono::{DateTime, Utc, Local};
use rusqlite::types::{FromSql, FromSqlError, FromSqlResult, ToSql, ToSqlOutput, ValueRef};
use rusqlite::{params, Connection, Row};
use serde::{Deserialize, Serialize};
use std::fs;
use std::sync::{Arc, Mutex};
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>};

// Newtype wrapper for `DateTime<Utc>` to implement `FromSql` and `ToSql`
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Timestamp(pub DateTime<Utc>);

impl ToSql for Timestamp {
    fn to_sql(&self) -> rusqlite::Result<ToSqlOutput<'_>> {
        Ok(ToSqlOutput::from(self.0.to_rfc3339()))
    }
}

impl From<DateTime<Utc>> for Timestamp {
    fn from(dt: DateTime<Utc>) -> Self {
        Timestamp(dt)
    }
}

impl FromSql for Timestamp {
    fn column_result(value: ValueRef<'_>) -> FromSqlResult<Self> {
        let s = value.as_str()?;
        match DateTime::parse_from_rfc3339(s) {
            Ok(dt) => Ok(Timestamp(dt.with_timezone(&Utc))),
            Err(e) => Err(FromSqlError::Other(Box::new(e))),
        }
    }
}

const DB_FILE: &str = "sensor_data.db";

/// A wrapper around an `Arc<Mutex<rusqlite::Connection>>` to be managed by Tauri's state.
#[derive(Clone)]
pub struct DbConnection(pub Arc<Mutex<Connection>>);

// The data structure for a single sensor reading.
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct Vector3 {
    pub x: f64,
    pub y: f64,
    pub z: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SensorReading {
    pub id: i64,
    pub timestamp: Timestamp,
    pub acc: Vector3,
    pub gyr: Vector3,
    pub mag: Vector3,
}

// Data structure for SensorReading when sent to the frontend (flattened Vector3, formatted timestamp)
#[derive(Debug, Clone, Serialize)]
pub struct FrontendSensorReading {
    pub id: i64,
    pub timestamp: String,
    pub acc_x: f64,
    pub acc_y: f64,
    pub acc_z: f64,
    pub gyr_x: f64,
    pub gyr_y: f64,
    pub gyr_z: f64,
    pub mag_x: f64,
    pub mag_y: f64,
    pub mag_z: f64,
}

impl From<SensorReading> for FrontendSensorReading {
    fn from(reading: SensorReading) -> Self {
        FrontendSensorReading {
            id: reading.id,
            timestamp: reading
                .timestamp
                .0
                .with_timezone(&Local)
                .format("%Y-%m-%d %H:%M:%S.%3f")
                .to_string(),
            acc_x: reading.acc.x,
            acc_y: reading.acc.y,
            acc_z: reading.acc.z,
            gyr_x: reading.gyr.x,
            gyr_y: reading.gyr.y,
            gyr_z: reading.gyr.z,
            mag_x: reading.mag.x,
            mag_y: reading.mag.y,
            mag_z: reading.mag.z,
        }
    }
}

impl<'stmt> TryFrom<&Row<'stmt>> for SensorReading {
    type Error = rusqlite::Error;

    fn try_from(row: &Row) -> Result<Self, Self::Error> {
        Ok(SensorReading {
            id: row.get(0)?,
            timestamp: row.get(1)?,
            acc: Vector3 {
                x: row.get(2)?,
                y: row.get(3)?,
                z: row.get(4)?,
            },
            gyr: Vector3 {
                x: row.get(5)?,
                y: row.get(6)?,
                z: row.get(7)?,
            },
            mag: Vector3 {
                x: row.get(8)?,
                y: row.get(9)?,
                z: row.get(10)?,
            },
        })
    }
}

/// Initializes the database connection and creates the table if it doesn't exist.
pub fn init(app_handle: &AppHandle) -> Result<Connection, anyhow::Error> {
    let app_dir = app_handle
        .path()
        .app_data_dir()
        .context("Failed to get app data directory")?;
    if !app_dir.exists() {
        fs::create_dir_all(&app_dir).context("Failed to create app data directory")?;
    }
    let db_path = app_dir.join(DB_FILE);

    log::info!("Initializing database at: {:?}", db_path);

    let conn = Connection::open(&db_path).context("Failed to open database connection")?;



    conn.execute(
        "CREATE TABLE IF NOT EXISTS sensor_readings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp TEXT NOT NULL,
            acc_x REAL NOT NULL,
            acc_y REAL NOT NULL,
            acc_z REAL NOT NULL,
            gyr_x REAL NOT NULL,
            gyr_y REAL NOT NULL,
            gyr_z REAL NOT NULL,
            mag_x REAL NOT NULL,
            mag_y REAL NOT NULL,
            mag_z REAL NOT NULL
        )",
        [],
    )
    .context("Failed to create 'sensor_readings' table")?;

    conn.execute(
        "CREATE TABLE IF NOT EXISTS devices (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            sn TEXT NOT NULL UNIQUE,
            product_name TEXT
        )",
        [],
    )
    .context("Failed to create 'devices' table")?;

    Ok(conn)
}

/// Inserts a new device into the database.
pub fn insert_device(
    conn: &Mutex<Connection>,
    sn: &str,
    device_path: &str,
) -> Result<(), anyhow::Error> {
    let conn = conn.lock().unwrap();
    conn.execute(
        "INSERT OR IGNORE INTO devices (sn, device_path) VALUES (?1, ?2)",
        params![sn, device_path],
    )
    .context("Failed to insert device")?;
    Ok(())
}

/// Inserts a new sensor reading into the database.
pub fn insert_reading(
    conn: &Mutex<Connection>,
    reading: &SensorReading,
) -> Result<(), anyhow::Error> {
    let conn = conn.lock().unwrap();
    conn.execute(
        "INSERT INTO sensor_readings (timestamp, device_path, acc_x, acc_y, acc_z, gyr_x, gyr_y, gyr_z, mag_x, mag_y, mag_z) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10)",
        params![
            reading.timestamp,
            reading.acc.x,
            reading.acc.y,
            reading.acc.z,
            reading.gyr.x,
            reading.gyr.y,
            reading.gyr.z,
            reading.mag.x,
            reading.mag.y,
            reading.mag.z,
        ],
    )
    .context("Failed to insert sensor reading")?;
    Ok(())
}

/// Retrieves all sensor readings from the database.
pub fn get_all_readings(
    conn: &Mutex<Connection>,
) -> Result<Vec<FrontendSensorReading>, anyhow::Error> {
    let conn = conn.lock().unwrap();
    let mut stmt = conn
        .prepare("SELECT id, timestamp, acc_x, acc_y, acc_z, gyr_x, gyr_y, gyr_z, mag_x, mag_y, mag_z FROM sensor_readings ORDER BY timestamp ASC")
        .context("Failed to prepare select statement")?;

    let readings = stmt
        .query_map([], |row| SensorReading::try_from(row))?
        .collect::<Result<Vec<SensorReading>, _>>()?
        .into_iter()
        .map(FrontendSensorReading::from)
        .collect();

    Ok(readings)
}

/// Retrieves all sensor readings from the database within a given time range.
pub fn get_readings_by_time_range(
    conn: &Mutex<Connection>,
    start_time: DateTime<Utc>,
    end_time: DateTime<Utc>,
) -> Result<Vec<FrontendSensorReading>, anyhow::Error> {
    let conn = conn.lock().unwrap();
    let mut stmt = conn
        .prepare(
            "SELECT id, timestamp, acc_x, acc_y, acc_z, gyr_x, gyr_y, gyr_z, mag_x, mag_y, mag_z FROM sensor_readings WHERE timestamp BETWEEN ?1 AND ?2 ORDER BY timestamp ASC",
        )
        .context("Failed to prepare select statement for time range")?;

    let readings = stmt
        .query_map(params![Timestamp(start_time), Timestamp(end_time)], |row| {
            SensorReading::try_from(row)
        })?
        .collect::<Result<Vec<SensorReading>, _>>()?
        .into_iter()
        .map(FrontendSensorReading::from)
        .collect();

    Ok(readings)
}
