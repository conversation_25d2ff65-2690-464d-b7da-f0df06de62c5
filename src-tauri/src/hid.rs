// src-tauri/src/hid.rs

use crate::database::{insert_reading, DbConnection, SensorReading, Timestamp, Vector3};
use crate::filters::{Attitude, CombinedKalmanFilter, CombinedVQFFilter, MadgwickFilter};
use chrono::{DateTime, Utc};
use hidapi::{DeviceInfo as HidDeviceInfo, HidApi};
use nalgebra;
use std::sync::mpsc::{self, Sender};
use std::sync::{Arc, Mutex};
use std::thread;
use tauri::{AppHandle, Emitter, State};

// --- Actor Model Implementation ---

/// Commands that can be sent to the HID actor thread.
#[derive(Debug)]
pub enum HidCommand {
    /// Command to write a raw byte array to the device.
    Write(Vec<u8>),
    Stop(),
}

/// State managed by <PERSON><PERSON>, providing a handle to the running HID actor.
pub struct HidActorHandle {
    pub command_tx: Mutex<Option<Sender<HidCommand>>>,
    pub thread_handle: Mutex<Option<thread::Join<PERSON>and<PERSON><()>>>,
}

impl HidActorHandle {
    pub fn new() -> Self {
        Self {
            command_tx: Mutex::new(None),
            thread_handle: Mutex::new(None),
        }
    }
}

/// Starts the HID actor in a new thread.
/// The actor opens the specified device and enters a loop to read data and process commands.
pub fn start_hid_actor<R: tauri::Runtime>(
    app: AppHandle<R>,
    device_path: String,
    actor_handle: &State<HidActorHandle>,
    api_state: &State<HidApiState>,
    db_connection: DbConnection,
) -> Result<(), String> {
    // If an actor is already running, stop it before starting a new one.
    stop_hid_actor(actor_handle)?;

    log::info!("Starting HID actor for device: {}", &device_path);

    let (command_tx, command_rx) = mpsc::channel();
    *actor_handle.command_tx.lock().unwrap() = Some(command_tx);

    // 发送读取指令
    actor_handle
        .command_tx
        .lock()
        .unwrap()
        .as_ref()
        .unwrap()
        .send(HidCommand::Write(vec![0x01, 0x01, 0x01]))
        .unwrap();

    let api_arc = api_state.0.clone();

    let handle = thread::spawn(move || {
        // --- Actor Thread Initialization ---
        let device = {
            let api = api_arc.lock().unwrap();
            match api.open_path(&std::ffi::CString::new(device_path).unwrap()) {
                Ok(d) => d,
                Err(e) => {
                    log::error!("Actor failed to open device: {}", e);
                    return;
                }
            }
        };
        log::info!("HID actor successfully opened device.");

        let mut madgwick_filter = MadgwickFilter::new();
        let mut kalman_filter = CombinedKalmanFilter::new();
        let mut vqf_filter = CombinedVQFFilter::new(0.1);

        let mut first_time_index: Option<i32> = None;
        let mut first_timestamp: Option<DateTime<Utc>> = None;

        // --- Actor Main Loop ---
        loop {
            // 1. Check for incoming commands without blocking.
            match command_rx.try_recv() {
                Ok(HidCommand::Write(data)) => {
                    log::info!("Actor received Write command: {:?}", &data);
                    if let Err(e) = device.write(&data) {
                        log::error!("Actor failed to write to device: {}", e);
                    }
                }
                Ok(HidCommand::Stop()) => {
                    log::info!("停止读取数据");
                    break;
                }
                Err(mpsc::TryRecvError::Empty) => { /* No command, continue to reading */ }
                Err(mpsc::TryRecvError::Disconnected) => {
                    log::info!("Actor command channel disconnected, stopping.");
                    break;
                }
            }

            // 2. Read data from the device with a timeout.
            let mut buf = [0u8; 64];
            match device.read_timeout(&mut buf, 100) {
                // 50ms timeout
                Ok(len) if len > 0 => {
                    if let Some(reading) =
                        parse_report(&buf[..len], &mut first_time_index, &mut first_timestamp)
                    {
                        process_and_emit_reading(
                            &reading,
                            &app,
                            &db_connection,
                            &mut madgwick_filter,
                            &mut kalman_filter,
                            &mut vqf_filter,
                        );
                    }
                }
                Ok(_) => { /* Timeout, loop will check for commands again */ }
                Err(e) => {
                    // Emit an event to the frontend notifying about the disconnection.
                    if let Err(emit_err) = app.emit("device-disconnected", ()) {
                        log::error!("Failed to emit device-disconnected event: {}", emit_err);
                    }
                    log::error!("Actor error reading from HID device: {}. Stopping.", e);
                    break; // Exit loop on read error.
                }
            }
        }
        log::info!("HID actor thread finished.");
    });

    *actor_handle.thread_handle.lock().unwrap() = Some(handle);
    Ok(())
}

/// Sends a `Stop` command to the actor and waits for it to terminate.
pub fn stop_hid_actor(actor_handle: &State<HidActorHandle>) -> Result<(), String> {
    log::info!("Attempting to stop HID actor...");
    if let Some(tx) = actor_handle.command_tx.lock().unwrap().take() {
        // 发送停止指令
        if tx.send(HidCommand::Write(vec![0x01, 0x01, 0x00])).is_ok() {
            log::info!("Stop command sent to actor.");
        } else {
            log::warn!("Failed to send Stop command, actor may already be gone.");
        }
    }

    if let Some(handle) = actor_handle.thread_handle.lock().unwrap().take() {
        log::info!("Waiting for actor thread to join...");
        if let Err(e) = handle.join() {
            return Err(format!("Failed to join actor thread: {:?}", e));
        }
        log::info!("Actor thread joined successfully.");
    }
    Ok(())
}

/// Sends a command to the actor to write data to the device.
pub fn write_to_device(actor_handle: &State<HidActorHandle>, data: Vec<u8>) -> Result<(), String> {
    if let Some(tx) = actor_handle.command_tx.lock().unwrap().as_ref() {
        tx.send(HidCommand::Write(data))
            .map_err(|e| format!("Failed to send Write command: {}", e))
    } else {
        Err("Cannot write: HID actor is not running.".to_string())
    }
}

#[allow(dead_code)]
pub fn stop_device(actor_handle: &State<HidActorHandle>) -> Result<(), String> {
    if let Some(tx) = actor_handle.command_tx.lock().unwrap().as_ref() {
        tx.send(HidCommand::Stop())
            .map_err(|e| format!("Failed to stop reading from device: {}", e))
    } else {
        Err("Cannot stop: HID actor is not running.".to_string())
    }
}

// --- Other Public Functions ---

/// Holds the singleton instance of the HID API.
pub struct HidApiState(pub Arc<Mutex<HidApi>>);

/// A simplified device information structure to send to the frontend.
#[derive(serde::Serialize, Clone, Debug)]
pub struct DeviceInfo {
    id: String,
    name: String,
}

/// Lists all available HID devices that match our criteria.
pub fn list_hid_devices(api_state: &State<HidApiState>) -> Result<Vec<DeviceInfo>, String> {
    let mut api = api_state.0.lock().map_err(|e| e.to_string())?;
    api.refresh_devices().map_err(|e| e.to_string())?;
    Ok(api
        .device_list()
        .filter(|dev| matches!(dev.vendor_id(), 0x08E2))
        .map(|dev: &HidDeviceInfo| DeviceInfo {
            id: dev.path().to_string_lossy().into_owned(),
            name: format!(
                "{}-{}",
                dev.product_string().unwrap_or("Unknown Device").to_string(),
                dev.serial_number().unwrap_or("").to_string()
            ),
        })
        .collect())
}

// --- Private Helper Functions & Data Structures ---

#[derive(Clone, Debug, serde::Serialize)]
struct AttitudeReading {
    timestamp: Timestamp,
    madgwick: Attitude,
    kalman: Attitude,
    vqf: Attitude,
}

fn parse_report(
    buf: &[u8],
    first_time_index: &mut Option<i32>,
    first_timestamp: &mut Option<DateTime<Utc>>,
) -> Option<SensorReading> {
    if buf.is_empty() {
        return None;
    }
    match buf[0] {
        2 => parse_report_type_2(buf),
        3 => parse_report_type_3(buf, first_time_index, first_timestamp),
        _ => None,
    }
}

fn process_and_emit_reading<R: tauri::Runtime>(
    raw_reading: &SensorReading,
    app_handle: &AppHandle<R>,
    db_connection: &DbConnection,
    madgwick_filter: &mut MadgwickFilter,
    kalman_filter: &mut CombinedKalmanFilter,
    vqf_filter: &mut CombinedVQFFilter,
) {
    if let Err(e) = insert_reading(&db_connection.0, raw_reading) {
        log::error!("Failed to insert sensor reading: {}", e);
    }

    let dt = 0.1;
    let gyro = nalgebra::Vector3::new(
        raw_reading.gyr.x.to_radians(),
        raw_reading.gyr.y.to_radians(),
        raw_reading.gyr.z.to_radians(),
    );
    let acc = nalgebra::Vector3::new(raw_reading.acc.x, raw_reading.acc.y, raw_reading.acc.z);
    let mag = nalgebra::Vector3::new(raw_reading.mag.x, raw_reading.mag.y, raw_reading.mag.z);

    let madgwick = madgwick_filter.update(&gyro, &acc, &mag);
    let kalman = kalman_filter.update(&gyro, &acc, dt);
    let vqf = vqf_filter.update(&gyro, &acc, &mag);

    let attitude_reading = AttitudeReading {
        timestamp: raw_reading.timestamp.clone(),
        madgwick,
        kalman,
        vqf,
    };

    if let Err(e) = app_handle.emit("update-attitude", &attitude_reading) {
        log::error!("Failed to emit 'update-attitude' event: {}", e);
    }
    if let Err(e) = app_handle.emit("update-data", raw_reading) {
        log::error!("Failed to emit 'update-data' event: {}", e);
    }
}

const GRAVITY_EARTH: f32 = 9.80665;

fn lsb_to_mps2(val: i16, g_range: f32, bit_width: u8) -> f32 {
    let half_scale = (1u32 << (bit_width - 1)) as f32;
    (GRAVITY_EARTH * val as f32 * g_range) / half_scale
}

fn lsb_to_dps(val: i16, dps: f32, bit_width: u8) -> f32 {
    let half_scale = (1u32 << (bit_width - 1)) as f32;
    (dps / half_scale) * (val as f32)
}

fn parse_report_type_2(buf: &[u8]) -> Option<SensorReading> {
    if buf.len() < 16 {
        return None;
    }
    let acc_x_raw = i16::from_le_bytes(<[u8; 2]>::try_from(&buf[4..6]).ok()?);
    let acc_y_raw = i16::from_le_bytes(<[u8; 2]>::try_from(&buf[6..8]).ok()?);
    let acc_z_raw = i16::from_le_bytes(<[u8; 2]>::try_from(&buf[8..10]).ok()?);
    let gyr_x_raw = i16::from_le_bytes(<[u8; 2]>::try_from(&buf[10..12]).ok()?);
    let gyr_y_raw = i16::from_le_bytes(<[u8; 2]>::try_from(&buf[12..14]).ok()?);
    let gyr_z_raw = i16::from_le_bytes(<[u8; 2]>::try_from(&buf[14..16]).ok()?);

    Some(SensorReading {
        id: 0,
        timestamp: Timestamp(Utc::now()),
        acc: Vector3 {
            x: lsb_to_mps2(acc_x_raw, 2.0, 16) as f64,
            y: lsb_to_mps2(acc_y_raw, 2.0, 16) as f64,
            z: lsb_to_mps2(acc_z_raw, 2.0, 16) as f64,
        },
        gyr: Vector3 {
            x: lsb_to_dps(gyr_x_raw, 2000.0, 16) as f64,
            y: lsb_to_dps(gyr_y_raw, 2000.0, 16) as f64,
            z: lsb_to_dps(gyr_z_raw, 2000.0, 16) as f64,
        },
        mag: Vector3::default(),
    })
}

fn parse_report_type_3(
    buf: &[u8],
    first_time_index_opt: &mut Option<i32>,
    first_timestamp_opt: &mut Option<DateTime<Utc>>,
) -> Option<SensorReading> {
    const DATA_LEN: usize = 4;
    let required_len = 29 + DATA_LEN * 3;
    if buf.len() < required_len {
        return None;
    }

    let time_index = i32::from_le_bytes(<[u8; 4]>::try_from(&buf[0..4]).ok()?);
    let timestamp: Timestamp;

    if first_time_index_opt.is_none() {
        let now = Utc::now();
        *first_time_index_opt = Some(time_index);
        *first_timestamp_opt = Some(now);
        timestamp = Timestamp(now);
    } else {
        let first_time_index = first_time_index_opt.unwrap();
        let first_timestamp = first_timestamp_opt.unwrap();

        let time_diff = time_index.wrapping_sub(first_time_index) as f64;
        let micros_offset = time_diff * 39.0625;

        let new_timestamp = first_timestamp + chrono::Duration::microseconds(micros_offset as i64);
        timestamp = Timestamp(new_timestamp);
    }

    let acc_x = f32::from_le_bytes(<[u8; 4]>::try_from(&buf[5..9]).ok()?);
    let acc_y = f32::from_le_bytes(<[u8; 4]>::try_from(&buf[9..13]).ok()?);
    let acc_z = f32::from_le_bytes(<[u8; 4]>::try_from(&buf[13..17]).ok()?);
    let gyr_x = f32::from_le_bytes(<[u8; 4]>::try_from(&buf[17..21]).ok()?);
    let gyr_y = f32::from_le_bytes(<[u8; 4]>::try_from(&buf[21..25]).ok()?);
    let gyr_z = f32::from_le_bytes(<[u8; 4]>::try_from(&buf[25..29]).ok()?);
    let mag_x = f32::from_le_bytes(<[u8; 4]>::try_from(&buf[29..33]).ok()?);
    let mag_y = f32::from_le_bytes(<[u8; 4]>::try_from(&buf[33..37]).ok()?);
    let mag_z = f32::from_le_bytes(<[u8; 4]>::try_from(&buf[37..41]).ok()?);

    Some(SensorReading {
        id: 0,
        timestamp: timestamp,
        acc: Vector3 {
            x: acc_x as f64,
            y: acc_y as f64,
            z: acc_z as f64,
        },
        gyr: Vector3 {
            x: gyr_x as f64,
            y: gyr_y as f64,
            z: gyr_z as f64,
        },
        mag: Vector3 {
            x: mag_x as f64,
            y: mag_y as f64,
            z: mag_z as f64,
        },
    })
}
