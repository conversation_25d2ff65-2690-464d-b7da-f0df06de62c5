use crate::database::{insert_reading, DbConnection, SensorReading, Timestamp, Vector3};
use crate::device_commands;
use crate::filters::{Attitude, CombinedKalmanFilter, CombinedVQFFilter, MadgwickFilter};
use anyhow::{anyhow, Context, Result};
use chrono::{DateTime, Duration, Utc};
use hidapi::{DeviceInfo as HidDeviceInfo, HidApi, HidDevice};
use nalgebra;
use std::ffi::CString;
use std::sync::mpsc::{self, Receiver, Sender};
use std::sync::{Arc, Mutex};
use std::thread;
use tauri::{AppHandle, Emitter, State};

// --- 公开结构体与枚举 ---

/// 发送给前端的简化设备信息。
#[derive(serde::Serialize, Clone, Debug)]
pub struct DeviceInfo {
    id: String,
    name: String,
}

/// 可发送至 HID Actor 线程的指令。
#[derive(Debug)]
pub enum HidCommand {
    /// 向设备写入原始字节数组。
    Write(Vec<u8>),
    /// 停止 Actor 线程。
    Stop,
}

/// 由 Tauri 管理的、运行中 HID Actor 的句柄。
pub struct HidActorHandle {
    pub command_tx: Mutex<Option<Sender<HidCommand>>>,
    pub thread_handle: Mutex<Option<thread::JoinHandle<()>>>,
}

impl HidActorHandle {
    pub fn new() -> Self {
        Self {
            command_tx: Mutex::new(None),
            thread_handle: Mutex::new(None),
        }
    }
}

/// 持有 HID API 的单例。
pub struct HidApiState(pub Arc<Mutex<HidApi>>);

// --- 公开的 Tauri 指令处理器 ---

/// 列出所有符合要求（VID=0x08E2）的 HID 设备。
pub fn list_hid_devices(api_state: &State<HidApiState>) -> Result<Vec<DeviceInfo>, String> {
    let mut api = api_state.0.lock().map_err(|e| e.to_string())?;
    api.refresh_devices().map_err(|e| e.to_string())?;
    Ok(api
        .device_list()
        .filter(|dev| matches!(dev.vendor_id(), 0x08E2))
        .map(|dev: &HidDeviceInfo| DeviceInfo {
            id: dev.path().to_string_lossy().into_owned(),
            name: format!(
                "{}{}",
                dev.product_string().unwrap_or("Unknown Device"),
                dev.serial_number().unwrap_or("")
            ),
        })
        .collect())
}

/// 在新线程中启动 HID Actor。
pub fn start_hid_actor<R: tauri::Runtime>(
    app: AppHandle<R>,
    device_path: String,
    actor_handle: &State<HidActorHandle>,
    api_state: &State<HidApiState>,
    db_connection: DbConnection,
) -> Result<(), String> {
    // 若已有 Actor 在运行，先停止它。
    stop_hid_actor(actor_handle)?;

    log::info!("正在为设备启动 HID Actor: {}", &device_path);

    let (command_tx, command_rx) = mpsc::channel();

    // 发送开始推流的指令。
    let start_cmd = device_commands::build_start_streaming_cmd();
    command_tx
        .send(HidCommand::Write(start_cmd))
        .map_err(|e| format!("发送启动指令失败: {}", e))?;

    let api_arc = api_state.0.clone();

    let handle = thread::spawn(move || {
        // 闭包现在只负责设置和运行 Actor，所有逻辑都被封装在 HidActor 中。
        match HidActor::new(app, device_path, api_arc, db_connection) {
            Ok(mut actor) => {
                log::info!("HID Actor 初始化成功。");
                actor.run(command_rx);
            }
            Err(e) => {
                log::error!("创建 HID Actor 失败: {}", e);
            }
        }
        log::info!("HID Actor 线程结束。");
    });

    *actor_handle.command_tx.lock().unwrap() = Some(command_tx);
    *actor_handle.thread_handle.lock().unwrap() = Some(handle);
    Ok(())
}

/// 发送 Stop 指令并等待 Actor 终止。
pub fn stop_hid_actor(actor_handle: &State<HidActorHandle>) -> Result<(), String> {
    log::info!("尝试停止 HID Actor...");

    // 从句柄中取出发送端。如果已经没了，说明 Actor 可能已经停止。
    if let Some(tx) = actor_handle.command_tx.lock().unwrap().take() {
        // 1. 先向设备发送停止推流的指令。
        let stop_streaming_cmd = device_commands::build_stop_streaming_cmd();
        if tx.send(HidCommand::Write(stop_streaming_cmd)).is_ok() {
            log::info!("已发送停止推流指令至设备。");
        } else {
            log::warn!("通道已关闭，无法发送停止推流指令。");
        }

        // 2. 再向 Actor 线程发送终止循环的指令。
        if tx.send(HidCommand::Stop).is_ok() {
            log::info!("已发送 Stop 指令至 Actor 线程。");
        } else {
            log::warn!("通道已关闭，无法发送 Stop 指令。");
        }
    }

    // 取出线程句柄并等待其结束。
    if let Some(handle) = actor_handle.thread_handle.lock().unwrap().take() {
        log::info!("等待 Actor 线程汇合...");
        if let Err(e) = handle.join() {
            return Err(format!("无法汇合 Actor 线程: {:?}", e));
        }
        log::info!("Actor 线程已成功汇合。");
    }
    Ok(())
}

/// 停止读取数据
pub fn stop_reading(actor_handle: &State<HidActorHandle>) -> Result<(), String> {
    log::info!("尝试停止读取数据...");

    // 向设备发送停止推流的指令
    let stop_streaming_cmd = device_commands::build_stop_streaming_cmd();
    write_to_device(actor_handle, stop_streaming_cmd)?;
    Ok(())
}

/// 通过 Actor 向设备写入数据。
pub fn write_to_device(actor_handle: &State<HidActorHandle>, data: Vec<u8>) -> Result<(), String> {
    match actor_handle.command_tx.lock().unwrap().as_ref() {
        Some(tx) => tx
            .send(HidCommand::Write(data))
            .map_err(|e| format!("发送 Write 指令失败: {}", e)),
        None => Err("无法写入：HID Actor 未在运行。".to_string()),
    }
}

// --- HID Actor 实现 ---

/// 负责所有与 HID 设备直接通信的 Actor。
struct HidActor<R: tauri::Runtime> {
    app_handle: AppHandle<R>,
    db_connection: DbConnection,
    device: HidDevice,
    madgwick: MadgwickFilter,
    kalman: CombinedKalmanFilter,
    vqf: CombinedVQFFilter,
    timestamp_calc: TimestampCalculator,
}

impl<R: tauri::Runtime> HidActor<R> {
    /// 创建并初始化一个新的 `HidActor`。
    fn new(
        app_handle: AppHandle<R>,
        device_path: String,
        api_arc: Arc<Mutex<HidApi>>,
        db_connection: DbConnection,
    ) -> Result<Self> {
        let device = {
            let api = api_arc.lock().map_err(|_| anyhow!("HID API 互斥锁中毒"))?;
            let c_path = CString::new(device_path.clone())
                .with_context(|| format!("为路径创建 CString 失败: {}", device_path))?;
            api.open_path(&c_path)
                .with_context(|| format!("Actor 打开设备失败: {}", device_path))?
        };

        Ok(Self {
            app_handle,
            db_connection,
            device,
            madgwick: MadgwickFilter::new(),
            kalman: CombinedKalmanFilter::new(),
            vqf: CombinedVQFFilter::new(0.1), // 假设 0.1 是采样周期
            timestamp_calc: TimestampCalculator::new(),
        })
    }

    /// Actor 的主循环。
    fn run(&mut self, command_rx: Receiver<HidCommand>) {
        loop {
            // 1. 非阻塞地检查新指令。
            match command_rx.try_recv() {
                Ok(command) => {
                    if self.handle_command(command) {
                        // `handle_command` 返回 true 则表示应停止。
                        break;
                    }
                }
                Err(mpsc::TryRecvError::Empty) => { /* 无指令，继续读取 */ }
                Err(mpsc::TryRecvError::Disconnected) => {
                    log::info!("Actor 指令通道断开，正在停止。");
                    break;
                }
            }

            // 2. 从设备读取并处理数据。
            if self.read_and_process_report().is_err() {
                // 此处出错（如设备断连）意味着应停止。
                break;
            }
        }
    }

    /// 处理从通道收到的指令。如果 Actor 应停止，则返回 `true`。
    fn handle_command(&self, command: HidCommand) -> bool {
        match command {
            HidCommand::Write(data) => {
                log::info!("Actor 收到 Write 指令: {:?}", &data);
                if let Err(e) = self.device.write(&data) {
                    log::error!("Actor 写入设备失败: {}", e);
                }
                false // 不停止
            }
            HidCommand::Stop => {
                log::info!("Actor 收到 Stop 指令。正在终止循环。");
                true // 停止
            }
        }
    }

    /// 从设备读取报告，解析并分发数据。
    fn read_and_process_report(&mut self) -> Result<()> {
        let mut buf = [0u8; 64];
        match self.device.read_timeout(&mut buf, 100) {
            Ok(0) => { /* 超时，无数据，正常 */ }
            Ok(len) => {
                if let Some(reading) =
                    SensorReading::from_report(&buf[..len], &mut self.timestamp_calc)
                {
                    self.process_and_emit_reading(&reading);
                }
            }
            Err(e) => {
                log::error!("Actor 从 HID 设备读取时出错: {}. 正在停止。", e);
                if let Err(emit_err) = self.app_handle.emit("device-disconnected", ()) {
                    log::error!("分发 device-disconnected 事件失败: {}", emit_err);
                }
                return Err(anyhow!("HID 读取错误: {}", e));
            }
        }
        Ok(())
    }

    /// 处理单个传感器读数，更新滤波器并分发事件。
    fn process_and_emit_reading(&mut self, raw_reading: &SensorReading) {
        if let Err(e) = insert_reading(&self.db_connection.0, raw_reading) {
            log::error!("插入传感器读数失败: {}", e);
        }

        // 准备滤波器数据
        let dt = 0.1; // TODO: 应基于时间戳动态计算
        let gyro = nalgebra::Vector3::new(
            raw_reading.gyr.x.to_radians(),
            raw_reading.gyr.y.to_radians(),
            raw_reading.gyr.z.to_radians(),
        );
        let acc = nalgebra::Vector3::new(raw_reading.acc.x, raw_reading.acc.y, raw_reading.acc.z);
        let mag = nalgebra::Vector3::new(raw_reading.mag.x, raw_reading.mag.y, raw_reading.mag.z);

        // 更新滤波器
        let madgwick = self.madgwick.update(&gyro, &acc, &mag);
        let kalman = self.kalman.update(&gyro, &acc, dt);
        let vqf = self.vqf.update(&gyro, &acc, &mag);

        // 分发事件
        let attitude_reading = AttitudeReading {
            timestamp: raw_reading.timestamp.clone(),
            madgwick,
            kalman,
            vqf,
        };

        if let Err(e) = self.app_handle.emit("update-attitude", &attitude_reading) {
            log::error!("分发 'update-attitude' 事件失败: {}", e);
        }
        if let Err(e) = self.app_handle.emit("update-data", raw_reading) {
            log::error!("分发 'update-data' 事件失败: {}", e);
        }
    }
}

// --- 数据解析与转换 ---

#[derive(Clone, Debug, serde::Serialize)]
struct AttitudeReading {
    timestamp: Timestamp,
    madgwick: Attitude,
    kalman: Attitude,
    vqf: Attitude,
}

/// 封装从设备报告中计算精确时间戳的逻辑。
struct TimestampCalculator {
    first_time_index: Option<u32>,
    first_timestamp: Option<DateTime<Utc>>,
}

impl TimestampCalculator {
    fn new() -> Self {
        Self {
            first_time_index: None,
            first_timestamp: None,
        }
    }

    /// 基于报告的时间索引计算时间戳。
    fn calculate(&mut self, time_index: u32) -> Timestamp {
        if self.first_time_index.is_none() {
            let now = Utc::now();
            self.first_time_index = Some(time_index);
            self.first_timestamp = Some(now);
            Timestamp(now)
        } else {
            let first_idx = self.first_time_index.unwrap();
            let first_ts = self.first_timestamp.unwrap();

            let time_diff = time_index.wrapping_sub(first_idx) as f64;
            // 39.0625 是设备特定的时钟缩放因子。
            const MICROS_PER_TICK: f64 = 39.0625;
            let micros_offset = time_diff * MICROS_PER_TICK;

            let new_timestamp = first_ts + Duration::microseconds(micros_offset as i64);
            Timestamp(new_timestamp)
        }
    }
}

impl SensorReading {
    /// 将原始 HID 报告缓冲区解析为 `SensorReading`。
    fn from_report(buf: &[u8], ts_calc: &mut TimestampCalculator) -> Option<Self> {
        if buf.is_empty() {
            return None;
        }
        // 基于第一个字节（报告ID）进行分发。
        match buf[0] {
            2 => Self::parse_report_type_2(buf),
            3 => Self::parse_report_type_3(buf, ts_calc),
            _ => {
                log::warn!("收到未知报告类型: {}", buf[0]);
                None
            }
        }
    }

    /// 解析类型 2 的报告 (原始 ADC 值)。
    fn parse_report_type_2(buf: &[u8]) -> Option<Self> {
        if buf.len() < 16 {
            return None;
        }
        // 安全地进行字节切片和转换的辅助函数。
        let get_i16 = |range: std::ops::Range<usize>| -> Option<i16> {
            Some(i16::from_le_bytes(buf.get(range)?.try_into().ok()?))
        };

        let acc_x_raw = get_i16(4..6)?;
        let acc_y_raw = get_i16(6..8)?;
        let acc_z_raw = get_i16(8..10)?;
        let gyr_x_raw = get_i16(10..12)?;
        let gyr_y_raw = get_i16(12..14)?;
        let gyr_z_raw = get_i16(14..16)?;

        const ACC_G_RANGE: f32 = 2.0;
        const GYR_DPS_RANGE: f32 = 2000.0;
        const BIT_WIDTH: u8 = 16;

        Some(SensorReading {
            id: 0,
            timestamp: Timestamp(Utc::now()), // 类型 2 报告没有设备时间戳
            acc: Vector3 {
                x: lsb_to_mps2(acc_x_raw, ACC_G_RANGE, BIT_WIDTH) as f64,
                y: lsb_to_mps2(acc_y_raw, ACC_G_RANGE, BIT_WIDTH) as f64,
                z: lsb_to_mps2(acc_z_raw, ACC_G_RANGE, BIT_WIDTH) as f64,
            },
            gyr: Vector3 {
                x: lsb_to_dps(gyr_x_raw, GYR_DPS_RANGE, BIT_WIDTH) as f64,
                y: lsb_to_dps(gyr_y_raw, GYR_DPS_RANGE, BIT_WIDTH) as f64,
                z: lsb_to_dps(gyr_z_raw, GYR_DPS_RANGE, BIT_WIDTH) as f64,
            },
            mag: Vector3::default(),
        })
    }

    /// 解析类型 3 的报告 (带时间戳的浮点值)。
    fn parse_report_type_3(buf: &[u8], ts_calc: &mut TimestampCalculator) -> Option<Self> {
        if buf.len() < 41 {
            return None;
        }
        // 安全地进行字节切片和转换的辅助函数。
        let get_u32 = |range: std::ops::Range<usize>| -> Option<u32> {
            Some(u32::from_le_bytes(buf.get(range)?.try_into().ok()?))
        };
        let get_f32 = |range: std::ops::Range<usize>| -> Option<f32> {
            Some(f32::from_le_bytes(buf.get(range)?.try_into().ok()?))
        };

        let time_index = get_u32(1..5)?;
        let timestamp = ts_calc.calculate(time_index);

        Some(SensorReading {
            id: 0,
            timestamp,
            acc: Vector3 {
                x: get_f32(5..9)? as f64,
                y: get_f32(9..13)? as f64,
                z: get_f32(13..17)? as f64,
            },
            gyr: Vector3 {
                x: get_f32(17..21)? as f64,
                y: get_f32(21..25)? as f64,
                z: get_f32(25..29)? as f64,
            },
            mag: Vector3 {
                x: get_f32(29..33)? as f64,
                y: get_f32(33..37)? as f64,
                z: get_f32(37..41)? as f64,
            },
        })
    }
}

// --- 单位转换辅助函数 ---

const GRAVITY_EARTH: f32 = 9.80665;

/// 将加速度计的 LSB 原始值转换为 m/s^2。
fn lsb_to_mps2(val: i16, g_range: f32, bit_width: u8) -> f32 {
    let half_scale = (1u32 << (bit_width - 1)) as f32;
    (val as f32 * g_range * GRAVITY_EARTH) / half_scale
}

/// 将陀螺仪的 LSB 原始值转换为 度/秒。
fn lsb_to_dps(val: i16, dps_range: f32, bit_width: u8) -> f32 {
    let half_scale = (1u32 << (bit_width - 1)) as f32;
    (val as f32 * dps_range) / half_scale
}
