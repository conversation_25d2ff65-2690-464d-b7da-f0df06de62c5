[package]
name = "MR01"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "hid_gamepad_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
anyhow = "1.0"

chrono = { version = "0.4", features = ["serde"] }
hidapi = "2.6.0"
kfilter = "0.4.0"
log = { version = "0.4", default-features = false }
nalgebra = { version = "0.34.1", features = ["serde"] }
vqf-rs = "0.3.0"
rand = "0.8"
rusqlite = { version = "0.31", features = ["bundled"] }
serde = { version = "1", features = ["derive"] }
serde_json = "1"
ahrs = "0.8.0"
tauri = { version = "2", features = [] }
tauri-plugin-log = { version = "2.0.0-beta.8", features = ["colored"] }
csv = "1.1"
dirs = "5.0"

tauri-plugin-opener = "2"
tauri-plugin-fs = "2"

[features]
# by default Tauri runs in production mode
# when `tauri dev` runs it is executed with `cargo run --no-default-features` if `devPath` is an URL
default = ["custom-protocol"]
# this feature is used for production builds where `devPath` points to the filesystem
# DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]
