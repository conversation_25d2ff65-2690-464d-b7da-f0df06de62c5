use std::time::{SystemTime, UNIX_EPOCH};

fn main() {
    // Get the current UNIX timestamp
    let now = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .expect("Time went backwards")
        .as_secs();

    // Set an environment variable that Rust code can access
    println!("cargo:rustc-env=BUILD_TIMESTAMP={}", now);

    // Tell Cargo to re-run this build script if build.rs changes
    println!("cargo:rerun-if-changed=build.rs");
    // Tell Cargo to re-run this build script if any file in src-tauri/src changes
    println!("cargo:rerun-if-changed=src-tauri/src");
    println!("cargo:rerun-if-changed=src/");

    tauri_build::build()
}
