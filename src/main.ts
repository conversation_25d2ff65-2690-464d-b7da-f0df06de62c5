import { createApp } from "vue";
import PrimeVue from 'primevue/config';
import ConfirmationService from 'primevue/confirmationservice';
import ToastService from 'primevue/toastservice';
import Aura from '@primeuix/themes/aura';

import App from "./App.vue";

import "./style.css";
import 'primeicons/primeicons.css'


const app = createApp(App)

app.use(PrimeVue, {
    theme: {
        preset: Aura,
        options: {
            prefix: 'p',
            darkModeSelector: false || 'none', // 禁用暗黑模式
            cssLayer: false
        }
    }
});
app.use(ConfirmationService);
app.use(ToastService);

app.mount("#app");
