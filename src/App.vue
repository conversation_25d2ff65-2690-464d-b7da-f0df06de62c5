<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import {
  listenForAttitude,
  listenForDisconnect, // Import the new listener
  Quaternion,
  getDeviceList,
  startReading,
  stopReading,
  unbindDevice,
  setBroadcastName,
  exportCSV as apiExportCSV,
  DeviceInfo,
  setSleepMode,
  getBuildInfo,
} from "./services/api";
import ChartView from "./components/ChartView.vue";
import { useConfirm } from "primevue/useconfirm";
import { useToast } from "primevue/usetoast";
import dayjs from "dayjs";
const toast = useToast();

// --- Component State ---
const devices = ref<DeviceInfo[]>([]);
const selectedDevice = ref<string | null>(null);
const isReading = ref(false);
const newBroadcastName = ref("");

const chartViewRef = ref<{ resetChart: () => void } | null>(null);

// Attitude data
const modelRotationMadgwick = ref<Quaternion>({ x: 0, y: 0, z: 0, w: 1 });
const modelRotationKalman = ref<Quaternion>({ x: 0, y: 0, z: 0, w: 1 });
const modelRotationVQF = ref<Quaternion>({ x: 0, y: 0, z: 0, w: 1 });

let unlistenAttitude: (() => void) | null = null;
let unlistenDisconnect: (() => void) | null = null;

const backendTimestamp = ref("")
const buildVersion = ref("")
// --- Lifecycle Hooks ---
onMounted(async () => {
  await refreshDeviceList();

  // Listen for attitude updates
  unlistenAttitude = await listenForAttitude((reading) => {
    if (!selectedDevice.value) return;
    modelRotationKalman.value = reading.kalman;
    modelRotationMadgwick.value = reading.madgwick;
    modelRotationVQF.value = reading.vqf;
  });

  // Check for updates based on build timestamps
  try {
    const backendBuildInfo = await getBuildInfo();
    buildVersion.value = backendBuildInfo.version;
    backendTimestamp.value = dayjs(parseInt(backendBuildInfo.build_timestamp) * 1000).format('YYYY-MM-DD HH:mm:ss'); // Convert to milliseconds
  } catch (error) {
    console.error("Failed to get backend build info:", error);
  }

  // Listen for device disconnection
  unlistenDisconnect = await listenForDisconnect(() => {
    toast.add({ severity: 'error', summary: '设备断开连接', detail: '请检查设备连接并刷新列表。', life: 5000 });
    isReading.value = false;
    selectedDevice.value = null;

    setTimeout(() => {
      refreshDeviceList();
    }, 1000 * 2);
  });
});

onUnmounted(() => {
  if (unlistenAttitude) {
    unlistenAttitude();
  }
  if (unlistenDisconnect) {
    unlistenDisconnect();
  }
  if (selectedDevice.value) {
    handleStopReading();
  }
});

const sleepModeValue = ref<number | null>(1);
const sleepMode = ref<{ name: string, value: number }[]>([
  { name: "普通休眠", value: 1 },
  { name: "船运休眠", value: 2 },
])

const handleSleepMode = async () => {
  if (!sleepModeValue.value) {
    return
  }

  try {
    await setSleepMode(sleepModeValue.value);
    console.log("Sleep mode set successfully!");

    newBroadcastName.value = ""; // Clear input
    selectedDevice.value = "";
    isReading.value = false;

    // 延时2秒后刷新设备列表，避免刷新时设备还没进入休眠状态
    setTimeout(() => {
      refreshDeviceList();
    }, 1000 * 2);
  } catch (error) {
    console.error("Failed to set sleep mode:", error);
  }
}

// --- Device & Reading Logic ---
async function refreshDeviceList() {
  try {
    devices.value = await getDeviceList();
  } catch (error) {
    console.error("Failed to get device list:", error);
  }
}

async function handleOpenDevice() {
  handleStartReading();
}

async function handleStartReading() {
  if (!selectedDevice.value) return;
  try {
    await startReading(selectedDevice.value);
    isReading.value = true;
  } catch (error) {
    console.error("Failed to start reading:", error);
  }
}

async function handleStopReading() {
  if (!selectedDevice.value) return;

  if (!isReading.value) {
    chartViewRef.value?.resetChart();
    handleStartReading();
    return;
  };

  try {
    await stopReading();
    isReading.value = false;
  } catch (error) {
    console.error("Failed to stop reading:", error);
  }
}

async function handleSetBroadcastName() {
  if (!newBroadcastName.value.trim()) {
    console.log("Broadcast name cannot be empty.");
    return;
  }
  try {
    await setBroadcastName(newBroadcastName.value.trim());
    console.log("Broadcast name updated successfully!");
    newBroadcastName.value = ""; // Clear input
  } catch (error) {
    console.error("Failed to set broadcast name:", error);
  }
}

async function handleUnbindDevice() {
  try {
    await unbindDevice();
    console.log("Device unbound successfully.");
    if (selectedDevice.value) {
      await handleStopReading();

      await unbindDevice();
    }

    setTimeout(() => {
      refreshDeviceList();
    }, 1000 * 2);
  } catch (error) {
    console.error("Failed to unbind device:", error);
  }
}

const confirm = useConfirm();
const requireConfirmation = (event: any) => {
  confirm.require({
    target: event.currentTarget,
    message: '确认擦除设备？',
    icon: 'pi pi-exclamation-triangle',
    rejectProps: {
      label: '取消',
      severity: 'secondary',
      outlined: true
    },
    acceptProps: {
      label: '擦除'
    },
    accept: () => {
      handleUnbindDevice();
    },
  });
}

const exportDateRange = ref<Date[] | null>(null);

async function exportCSV() {
  if (!exportDateRange.value || exportDateRange.value.length !== 2) {
    toast.add({ severity: 'error', summary: 'Error', detail: 'Please select a valid date range.', life: 3000 });
    return;
  }

  try {
    const filePath = await apiExportCSV(exportDateRange.value[0], exportDateRange.value[1]);
    toast.add({ severity: 'success', summary: 'Export Successful', detail: `File saved to ${filePath}`, life: 5000 });
  } catch (error) {
    toast.add({ severity: 'error', summary: 'Export Failed', detail: error as string, life: 5000 });
  }
}
</script>

<template>
  <div class="flex flex-col h-screen w-screen text-white font-mono">
    <!-- Control Panel -->
    <div class="flex flex-wrap gap-4 px-4">
      <!-- Device Selection -->
      <div class="flex items-center gap-2">
        <div class="card flex justify-center">
          <Select v-model="selectedDevice" :options="devices" optionLabel="name" optionValue="id" placeholder="选择设备"
            showClear emptyMessage="没找到设备，请连接后刷新" :disabled="isReading" class="border border-gray-300 rounded px-2 w-60"
            @change="handleOpenDevice" />
        </div>

        <Button @click="refreshDeviceList">刷新</Button>
      </div>

      <!-- Start/Stop Reading -->
      <Button @click="handleStopReading" :disabled="!selectedDevice">{{ isReading ? '停止读取' : '开始读取' }}</Button>

      <!-- Reading Mode -->
      <!-- <button @click="toggleReadingMode" :disabled="!isReading">
        模式: {{ readingMode === 'parsed' ? '解析值' : '原始值' }}
      </button> -->

      <!-- Other Controls -->
      <!-- <button @click="handleSetTimestamp" :disabled="!isReading">设置时间戳</button> -->

      <!-- Broadcast Name -->
      <div class="flex items-center gap-2">
        <InputText type="text" v-model="newBroadcastName" placeholder="新广播名称(最长6位)" :disabled="!selectedDevice"
          maxlength="6" />
        <Button @click="handleSetBroadcastName" :disabled="!selectedDevice || !newBroadcastName.trim()">修改名称</Button>
      </div>

      <div class="flex items-center gap-2">
        <Select v-model="sleepModeValue" :options="sleepMode" optionLabel="name" optionValue="value" placeholder="休眠模式"
          class="border border-gray-600 rounded px-2" />
        <Button @click="handleSleepMode" :disabled="!selectedDevice">休眠</Button>
      </div>

      <!-- Unbind -->
      <Toast />
      <ConfirmPopup>
        <template #message="slotProps">
          <div class="flex flex-col items-center w-full gap-4 border-b border-surface-200 p-4 mb-4 pb-0">
            <p>擦除后，再次操作设备时，需要在系统蓝牙中解除配对后重新配对。</p>
            <p>{{ slotProps.message.message }}</p>
          </div>
        </template>
      </ConfirmPopup>
      <div class="card flex flex-wrap gap-2 justify-center">
        <Button @click="requireConfirmation($event)" label="擦除设备绑定信息" :disabled="!selectedDevice"></Button>
      </div>

      <!-- Export -->
      <div class="flex items-center gap-2">
        <DatePicker v-model="exportDateRange" selectionMode="range" :manualInput="false" showTime hourFormat="24"
          dateFormat="yy/mm/dd" :minDate="new Date(2025, 1, 1)" :maxDate="new Date()" inputClass="w-96" />
        <Button @click="exportCSV" label="导出CSV" :disabled="!exportDateRange || exportDateRange.length !== 2" />
      </div>

    </div>

    <Divider />

    <!-- Main Content -->
    <div class="flex flex-row flex-grow ">
      <div class="h-full border-r border-gray-600 w-full p-4 pb-8">
        <ChartView ref="chartViewRef" :loading="selectedDevice !== null" />
      </div>
      <!-- <div class="h-full w-1/2 flex-initial gap-4">
        <div class="flex flex-col border-r border-gray-600 w-full h-1/3">
          <h2 class="text-center text-lg bg-gray-700">卡尔曼</h2>
          <ThreeScene :rotation="modelRotationKalman" />
        </div>
        <div class="flex flex-col border-r border-gray-600 w-full h-1/3">
          <h2 class="text-center text-lg bg-gray-700">Madgwick</h2>
          <ThreeScene :rotation="modelRotationMadgwick" />
        </div>
        <div class="flex flex-col border-r border-gray-600 w-full h-1/3">
          <h2 class="text-center text-lg">VQS</h2>
          <ThreeScene :rotation="modelRotationVQF" />
        </div>
      </div> -->
    </div>

    <!-- App Version and Build Date -->
    <div class="absolute bottom-2 right-2 text-xs text-gray-500">
      <span class="ml-4">Build: {{ backendTimestamp }}</span>
      <span class="ml-4">Version: {{ buildVersion }}</span>
    </div>
  </div>
</template>
