<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { use } from "echarts/core";
import { CanvasRenderer } from "echarts/renderers";
import { LineChart } from "echarts/charts";
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
} from "echarts/components";
import VChart from "vue-echarts";
import {
  getAllReadings,
  listenForNewReadings,
  SensorReading,
} from "./../services/api";
import dayjs from "dayjs";

// Register ECharts components
use([
  CanvasRenderer,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
]);

const sensorReading = ref<SensorReading | null>(null);

const createChartOption = (title: string) => ({
  title: {
    text: title,
    left: "center",
  },
  tooltip: {
    trigger: "axis",
  },
  legend: {
    data: ["X", "Y", "Z"],
    top: 50,
    type: "scroll",
    orient: "horizontal",
    left: "center",
    selectedMode: false,
    animation: false,
  },
  grid: {
    top: 100,
    left: "3%",
    right: "4%",
    bottom: "3%",
  },
  xAxis: {
    type: "category",
    data: [] as string[], // x 轴数据
    splitLine: {
      show: false,
    },
    axisLabel: {
      show: true,
      interval: "auto", // 自动计算间隔
    },
    splitNumber: 5, // 强制显示 5 个刻度
  },
  yAxis: {
    type: "value",
  },
  series: [
    {
      name: "X",
      type: "line",
      showSymbol: false,
      color: "#FF6384",
      data: [] as number[], // 一维数据数组
    },
    {
      name: "Y",
      type: "line",
      showSymbol: false,
      color: "#36A2EB",
      data: [] as number[], // 一维数据数组
    },
    {
      name: "Z",
      type: "line",
      showSymbol: false,
      color: "#4BC0C0",
      data: [] as number[], // 一维数据数组
    },
  ],
});

const accOption = ref(createChartOption("加速度"));
const gyrOption = ref(createChartOption("陀螺仪"));
const magOption = ref(createChartOption("地磁"));

// 计算 x 轴标签间隔，确保显示 5 个刻度
const calculateXAxisInterval = (dataLength: number) => {
  if (dataLength <= 5) return 0; // 如果数据点少于等于5个，显示所有
  return Math.ceil(dataLength / 5); // 计算间隔，确保最多显示5个标签
};

// 图表引用
const accChartRef = ref();
const gyrChartRef = ref();
const magChartRef = ref();

let unlisten: (() => void) | null = null;

const maxDataPoints = 100;

// 防抖更新机制
let updateTimer: number | null = null;
const pendingUpdates = ref({
  acc: null as SensorReading | null,
  gyr: null as SensorReading | null,
  mag: null as SensorReading | null,
});

const updateSeries = (reading: SensorReading, key: "acc" | "gyr" | "mag") => {
  const data = reading[key];
  if (!data) return;
  const timestamp = reading.timestamp;

  // 获取对应的图表引用和选项
  let chartRef, option;
  switch (key) {
    case "acc":
      chartRef = accChartRef.value;
      option = accOption.value;
      break;
    case "gyr":
      chartRef = gyrChartRef.value;
      option = gyrOption.value;
      break;
    case "mag":
      chartRef = magChartRef.value;
      option = magOption.value;
      break;
  }

  if (!chartRef || !option) return;

  // 更新数据
  if (option.series[0].data.length > maxDataPoints) {
    option.series[0].data.shift();
    option.series[1].data.shift();
    option.series[2].data.shift();
    option.xAxis.data.shift(); // 同时移除 x 轴数据
  }

  // 添加新的数据点
  option.series[0].data.push(data.x);
  option.series[1].data.push(data.y);
  option.series[2].data.push(data.z);
  option.xAxis.data.push(dayjs(timestamp).format("HH:mm:ss")); // 添加时间戳到 x 轴

  // 计算 x 轴标签间隔
  const interval = calculateXAxisInterval(option.xAxis.data.length);

  // 使用 setOption 更新图表，保持图例状态
  const updateOption = {
    xAxis: {
      data: option.xAxis.data,
      axisLabel: {
        show: true,
        interval: interval,
      },
    },
    yAxis: {
      axisLabel: {
        show: true,
        interval: interval,
      },
    },
    series: option.series,
  };

  // 使用 setOption 而不是直接修改 option，这样可以更好地保持图例状态
  chartRef.setOption(updateOption, false, true);
};

// 防抖批量更新函数
const debouncedUpdate = (reading: SensorReading) => {
  // 保存最新的数据
  pendingUpdates.value.acc = reading;
  pendingUpdates.value.gyr = reading;
  pendingUpdates.value.mag = reading;

  // 清除之前的定时器
  if (updateTimer) {
    clearTimeout(updateTimer);
  }

  // 设置新的定时器，延迟更新
  //   updateTimer = setTimeout(() => {
  if (pendingUpdates.value.acc) {
    updateSeries(pendingUpdates.value.acc, "acc");
  }
  if (pendingUpdates.value.gyr) {
    updateSeries(pendingUpdates.value.gyr, "gyr");
  }
  if (pendingUpdates.value.mag) {
    updateSeries(pendingUpdates.value.mag, "mag");
  }

  // 清空待更新数据
  pendingUpdates.value.acc = null;
  pendingUpdates.value.gyr = null;
  pendingUpdates.value.mag = null;
  //   }, 50); // 50ms 防抖延迟
};

onMounted(async () => {
  const initialReadings = await getAllReadings();
  initialReadings.forEach((reading) => {
    updateSeries(reading, "acc");
    updateSeries(reading, "gyr");
    updateSeries(reading, "mag");
  });

  unlisten = await listenForNewReadings((reading) => {
    sensorReading.value = reading;
    // 使用防抖更新，减少频繁更新对图例状态的影响
    debouncedUpdate(reading);
  });
});

onUnmounted(() => {
  if (unlisten) {
    unlisten();
  }
  // 清理定时器
  if (updateTimer) {
    clearTimeout(updateTimer);
  }
});

const resetChart = () => {
  // 重置 series 数据
  accOption.value.series.forEach((series) => (series.data = []));
  gyrOption.value.series.forEach((series) => (series.data = []));
  magOption.value.series.forEach((series) => (series.data = []));

  // 重置 x 轴数据
  accOption.value.xAxis.data = [];
  gyrOption.value.xAxis.data = [];
  magOption.value.xAxis.data = [];
};

defineExpose({
  resetChart,
});
</script>

<template>
  <div class="flex-1 flex flex-col gap-4 h-full w-full">
    <v-chart manual-update ref="accChartRef" class="flex-1" :option="accOption" autoresize />
    <v-chart manual-update ref="gyrChartRef" class="flex-1" :option="gyrOption" autoresize />
    <v-chart manual-update ref="magChartRef" class="flex-1" :option="magOption" autoresize />
  </div>
</template>
