<template>
  <div ref="container" class="w-full h-full"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { Quaternion } from '../services/api';

const props = defineProps<{
  rotation: Quaternion | null;
}>();

const container = ref<HTMLDivElement | null>(null);

let renderer: THREE.WebGLRenderer;
let scene: THREE.Scene;
let camera: THREE.PerspectiveCamera;
let controls: OrbitControls;
let cube: THREE.Mesh;
let animationFrameId: number;

onMounted(() => {
  if (!container.value) return;

  const width = container.value.clientWidth;
  const height = container.value.clientHeight;

  // Scene
  scene = new THREE.Scene();
  scene.background = new THREE.Color(0xf0f0f0);

  // Camera
  camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
  camera.position.set(2, 2, 2);

  // Renderer
  renderer = new THREE.WebGLRenderer({ antialias: true });
  renderer.setSize(width, height);
  container.value.appendChild(renderer.domElement);

  // Controls
  controls = new OrbitControls(camera, renderer.domElement);
  controls.enableDamping = true;

  // Lights
  const ambientLight = new THREE.AmbientLight(0x404040);
  scene.add(ambientLight);
  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.5);
  scene.add(directionalLight);

  // Cube
  const geometry = new THREE.BoxGeometry(1.5, 2, 1); // width, height, depth

  const createLabelMaterial = (text: string) => {
    const canvas = document.createElement('canvas');
    canvas.width = 256;
    canvas.height = 256;
    const context = canvas.getContext('2d')!;
    context.fillStyle = 'lightgray';
    context.fillRect(0, 0, canvas.width, canvas.height);
    context.font = 'bold 96px Arial';
    context.fillStyle = 'black';
    context.textAlign = 'center';
    context.textBaseline = 'middle';
    context.fillText(text, canvas.width / 2, canvas.height / 2);
    const texture = new THREE.CanvasTexture(canvas);
    return new THREE.MeshStandardMaterial({ map: texture });
  };

  const materials = [
    createLabelMaterial('right'), // +X
    createLabelMaterial('left'),  // -X
    createLabelMaterial('top'),   // +Y
    createLabelMaterial('bottom'),// -Y
    createLabelMaterial('front'), // +Z
    createLabelMaterial('back'),  // -Z
  ];

  cube = new THREE.Mesh(geometry, materials);
  scene.add(cube);

  // Axes Helper
  const axesHelper = new THREE.AxesHelper(2);
  scene.add(axesHelper);

  watch(() => props.rotation, (newRotation) => {
    if (newRotation) {
      // The quaternion received from props is now assumed to be in the correct
      // coordinate system for the Three.js scene.
      cube.quaternion.set(newRotation.x, newRotation.y, newRotation.z, newRotation.w);
    }
  });

  const animate = () => {
    animationFrameId = requestAnimationFrame(animate);
    controls.update();
    renderer.render(scene, camera);
  };

  animate();

  window.addEventListener('resize', onWindowResize);
});

onUnmounted(() => {
  cancelAnimationFrame(animationFrameId);
  renderer.dispose();
  window.removeEventListener('resize', onWindowResize);
});

const onWindowResize = () => {
  if (container.value) {
    const width = container.value.clientWidth;
    const height = container.value.clientHeight;

    camera.aspect = width / height;
    camera.updateProjectionMatrix();

    renderer.setSize(width, height);
  }
};
</script>
