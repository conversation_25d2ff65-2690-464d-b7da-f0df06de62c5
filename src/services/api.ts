// src/services/api.ts

import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';

export interface Vector3 {
    x: number;
    y: number;
    z: number;
}

// This interface must match the `SensorReading` struct in the Rust backend.
export interface SensorReading {
    id: number;
    timestamp: string; // Dates are serialized as strings by default.
    acc: Vector3;
    gyr: Vector3;
    mag: Vector3;
}

// This interface must match the `Attitude` struct in the Rust backend.
export interface Quaternion {
    x: number;
    y: number;
    z: number;
    w: number;
}

// export interface Attitude {
//     quaternion: Quaternion;
// }

// This interface must match the `AttitudeReading` struct in the Rust backend.
export interface AttitudeReading {
    timestamp: string;
    madgwick: Quaternion;
    kalman: Quaternion;
    vqf: Quaternion;
}

export interface DeviceInfo {
    name: string;
    id: string;
}

export interface BuildInfo {
    version: string;
    build_timestamp: string; // UNIX timestamp as a string
}

/**
 * Fetches the backend's build information.
 */
export async function getBuildInfo(): Promise<BuildInfo> {
    return invoke('get_build_info');
}

export type ReadingMode = 'parsed' | 'raw';

/**
 * Fetches the list of available HID devices.
 */
export async function getDeviceList(): Promise<DeviceInfo[]> {
    return invoke('get_device_list');
}

/**
 * Starts reading data from the device.
 */
export async function startReading(deviceId: string): Promise<void> {
    return invoke('start_reading', { deviceId });
}

/**
 * Stops reading data from the device.
 */
export async function stopReading(): Promise<void> {
    return invoke('stop_reading');
}

/**
 * Sets the reading mode.
 * @param mode The reading mode to set.
 */
export async function setReadingMode(mode: ReadingMode): Promise<void> {
    return invoke('set_reading_mode', { mode });
}

/**
 * Sends a command to set the device's internal timestamp.
 */
export async function setTimestamp(): Promise<void> {
    return invoke('set_timestamp');
}

/**
 * Sets the broadcast name of the device.
 * @param name The new broadcast name.
 */
export async function setBroadcastName(name: string): Promise<void> {
    return invoke('set_broadcast_name', { name });
}

export async function setSleepMode(mode: number): Promise<void> {
    return invoke('set_sleep_mode', { mode });
}

/**
 * Unbinds the device.
 */
export async function unbindDevice(): Promise<void> {
    return invoke('unbind_device');
}


/**
 * Fetches all historical sensor readings from the backend.
 * @returns A promise that resolves to an array of sensor readings.
 */
export async function getAllReadings(): Promise<SensorReading[]> {
    try {
        const readings = await invoke<SensorReading[]>('get_all_readings');
        return readings;
    } catch (error) {
        console.error("Failed to invoke 'get_all_readings':", error);
        return [];
    }
}

/**
 * Listens for new sensor readings pushed from the backend.
 * @param callback The function to execute when a new reading is received.
 * @returns A promise that resolves to a function to stop listening.
 */
export async function listenForNewReadings(
    callback: (reading: SensorReading) => void
): Promise<() => void> {
    try {
        const unlisten = await listen<SensorReading>('update-data', (event) => {
            callback(event.payload);
        });
        return unlisten;
    } catch (error) {
        console.error("Failed to set up 'update-data' listener:", error);
        // Return a no-op function if listening fails
        return () => {};
    }
}

/**
 * Listens for new attitude readings pushed from the backend.
 * @param callback The function to execute when a new reading is received.
 * @returns A promise that resolves to a function to stop listening.
 */
export async function listenForAttitude(
    callback: (reading: AttitudeReading) => void
): Promise<() => void> {
    try {
        const unlisten = await listen<AttitudeReading>('update-attitude', (event) => {
            callback(event.payload);
        });
        return unlisten;
    } catch (error) {
        console.error("Failed to set up 'update-attitude' listener:", error);
        return () => {};
    }
}

/**
 * Listens for device disconnection events.
 * @param callback The function to execute when the device is disconnected.
 * @returns A promise that resolves to a function to stop listening.
 */
export async function listenForDisconnect(callback: () => void): Promise<() => void> {
    try {
        const unlisten = await listen<void>('device-disconnected', () => {
            callback();
        });
        return unlisten;
    } catch (error) {
        console.error("Failed to set up 'device-disconnected' listener:", error);
        return () => {};
    }
}

/**
 * Exports sensor readings to a CSV file.
 * @param startTime The start of the time range.
 * @param endTime The end of the time range.
 * @returns A promise that resolves to the CSV data as a string.
 */
export async function exportCSV(startTime: Date, endTime: Date): Promise<string> {
    return invoke('export_csv', {
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
    });
}
