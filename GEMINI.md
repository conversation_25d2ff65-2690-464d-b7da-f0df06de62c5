# Gemini 代码助手上下文

## 项目概述

这是一个 Tauri 应用程序，旨在从 HID 设备读取传感器数据并实时显示在图表中。前端使用 Vue.js 和 TypeScript 构建，并使用 ECharts 作为图表组件。后端使用 Rust 编写，并使用 SQLite 数据库存储传感器数据。

该应用程序的工作方式如下：

1.  Rust 后端使用 `hidapi` 与 HID 设备进行通信。
2.  它会生成一个线程，每秒从设备读取传感器数据。
3.  此数据将插入 SQLite 数据库。
4.  向前端发出包含新数据的事件。
5.  前端接收事件并实时更新图表。
6.  前端还会在启动时从后端获取所有历史数据。

## 构建和运行

要构建和运行此项目，您需要安装 Node.js、pnpm 和 Rust。

**开发：**

要启动开发服务器，请运行以下命令：

```bash
pnpm dev
```

这将为前端启动 Vite 开发服务器，为后端启动 Tauri 开发环境。

**构建：**

要为生产构建应用程序，请运行以下命令：

```bash
pnpm build
```

这将为前端创建一个生产版本，并为后端创建一个独立的可执行文件。

**Tauri CLI：**

您还可以直接使用 Tauri CLI 来管理应用程序：

```bash
pnpm tauri <command>
```

例如，要启动开发环境，您可以使用：

```bash
pnpm tauri dev
```

## 开发约定

*   前端代码位于 `src` 目录中。
*   后端代码位于 `src-tauri` 目录中。
*   前端和后端使用 Tauri 的 `invoke` 和 `listen` 函数进行通信。
*   数据以 JSON 格式交换，`SensorReading` 接口/结构定义了数据结构。
*   后端使用 SQLite 数据库存储传感器数据。数据库文件位于应用程序的数据目录中。
*   后端使用 `hidapi` 库与 HID 设备进行交互。
