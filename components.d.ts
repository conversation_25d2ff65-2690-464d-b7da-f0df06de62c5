/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Button: typeof import('primevue/button')['default']
    ChartView: typeof import('./src/components/ChartView.vue')['default']
    ConfirmPopup: typeof import('primevue/confirmpopup')['default']
    DatePicker: typeof import('primevue/datepicker')['default']
    Divider: typeof import('primevue/divider')['default']
    InputText: typeof import('primevue/inputtext')['default']
    Select: typeof import('primevue/select')['default']
    ThreeScene: typeof import('./src/components/ThreeScene.vue')['default']
    Toast: typeof import('primevue/toast')['default']
  }
}
