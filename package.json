{"name": "mr01", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@primeuix/themes": "^1.2.5", "@tailwindcss/vite": "^4.1.14", "@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "chart.js": "^4.5.1", "dayjs": "^1.11.18", "echarts": "^6.0.0", "primeicons": "^7.0.0", "primevue": "^4.4.1", "tailwindcss-primeui": "^0.6.1", "three": "^0.180.0", "vue": "^3.5.13", "vue-echarts": "^8.0.0"}, "devDependencies": {"@primevue/auto-import-resolver": "^4.4.1", "@tauri-apps/cli": "^2.8.4", "@tauri-apps/plugin-dialog": "^2.4.0", "@tauri-apps/plugin-fs": "~2", "@types/three": "^0.180.0", "@vitejs/plugin-vue": "^5.2.1", "tailwindcss": "^4.1.14", "typescript": "~5.6.2", "unplugin-vue-components": "^29.1.0", "vite": "^6.0.3", "vue-tsc": "^2.1.10"}}